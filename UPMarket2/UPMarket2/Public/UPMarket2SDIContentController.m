//
//  UPMarket2SDIContentController.m
//  UPMarket2
//
//  Created by sammy<PERSON> on 2020/2/7.
//  Copyright © 2020 UpChina. All rights reserved.
//

#import <UPCommon/UPTabView.h>
#import <UPCommon/UPCustomOptGroupManager.h>

#import "UPMarket2SDIContentController.h"
#import <UPCommon/UPStockPickerController.h>
#import "UPMarket2SDIBasicDataView.h"
#import "UPMarket2SDILandscapeBasicDataView.h"
#import "UPMarket2SDIBaseChartView.h"
#import "UPMarket2SDIMinuteChartView.h"
#import "UPMarket2SDIKLineChartView.h"
#import "UPMarket2StockSettingController.h"
#import "UPMarket2SDIHeaderView.h"
//#import "UPUserSDK/UPOptionalManager.h"
#import "UPMarket2SDIExtendBasicDataView.h"
#import "UPMarket2SDISceneView.h"
#import "UPMarket2SDILongPressMaskView.h"
#import "UPMarket2SDISelectIndexModel.h"
#import "UPMarket2SDISelectIndexView.h"
#import "UPMarket2SDIStockMaskView.h"
#import "UPMarket2SDIStockTypeView.h"
#import "UPMarketSDIOptionalToast.h"
#import "UPMarket2SDIBottomTradeView.h"
#import "UPMarket2SDIIndexStatisticView.h"
#import "UPMarket2SDIHistoryMinuteView.h"
#import "UPMarket2SDIBaseExtraView.h"
#import "UPMarket2SDIQXDetailView.h"
#import <UPMarketIndex/UPMarketIndexKlineIndexFetchLogic.h>
#import <UPMarketIndex/UPMarketIndexMinIndexFetchLogic.h>
#import <UPMarketIndex/UPMarketIndexGroupModel.h>
#import "FMUPDataTool.h"

#define UP_STOCKS_SWITCH_GUIDE_POP_SHOW @"UP_STOCKS_SWITCH_GUIDE_POP_SHOW"

static NSMutableArray *_sGlobalExtraTab;

// MARK: UPMarket2SDIContentController

@interface UPMarket2SDIContentController () <
UPTabViewDelegate,
UPMarket2SDIBasicDataViewDelegate,
UPMarket2SDILandscapeBasicDataViewDelegate,
UPMarket2SDIHeaderViewDelegate,
UPMarket2SDIStockMaskViewDelegate,
UIScrollViewDelegate,
UPMarketSDIOptionalToastDelegate,
UPStockPickerControllerDelegate,
UPMarket2SDISceneViewDelegate>

@property(nonatomic, strong) UPMarket2SDIHeaderView * headerView;

@property (strong, nonatomic) UIView *bgView;

@property(nonatomic, strong) UIScrollView * scrollView; // 横屏不需要

@property(nonatomic, strong) UIView * topContainerView,*bottomContainerView;

@property(nonatomic, strong) UPMarket2SDIBasicDataView * basicDataView; // 盘口
@property(nonatomic, strong) UPMarket2SDILandscapeBasicDataView * landscapeBasicDataView; // 横屏盘口

@property (nonatomic, strong) UPMarket2SDILongPressMaskView *longPressMaskView;

@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UIButton *indexSettingButton;
@property(nonatomic, strong) UPTabView * chartsTabView; // 指标图Tab
@property (nonatomic, strong) UIView *landscapeBottomToolView;  // 横屏"买卖撤"的View,竖屏不需要

@property(nonatomic, strong) UPTabView * extraTabView; // 附加数据Tab, 新公研等, 横屏不需要
@property (nonatomic, strong) UIView *etRightCover; // 资讯栏右边遮罩，告诉用户extraTabView可以左右滑动
@property (nonatomic, strong) CAGradientLayer *etRightCoverLayer;

@property(nonatomic, strong) UPMarket2SDIBaseChartView * currentChartView; // 当前展示的指标图
@property(nonatomic, strong) UIView * currentExtraView; // 当前展示的附加数据View, 横屏不需要

@property (nonatomic, strong) UPMarket2SDIHistoryMinuteView *historyMinuteView;
@property (nonatomic, assign) BOOL isLeftDisable,isRightDisable;

@property (nonatomic, strong) UPMarket2SDIBottomTradeView *bottomTradeView; //底部的五档视图,横屏不需要
@property (nonatomic, strong) UPMarket2SDISceneView *sceneView;                // 竖屏的 sceneView,横屏不需要
@property (nonatomic, strong) UPMarket2SDIIndexStatisticView *statisticView;

@property(nonatomic, strong) NSMutableDictionary * viewCache; // 缓存chart view和extra view

@property (nonatomic, strong) UPPopupView *chartViewPopupView;

@property (nonatomic, strong) UPPopupView *dealVolPopupView;

@property (strong, nonatomic) UIView *dealVolContent;

@property (nonatomic, strong) UPMarketSDIOptionalToast *optionToast;

@property (nonatomic, strong) UPMarket2SDIExtendBasicDataView *stockBasicDataView;

@property (nonatomic, strong) UPMarket2SDIStockMaskView *stockMaskView;

@property (nonatomic, strong) NSDictionary *extraTabTitleDic;

@property (nonatomic, assign) NSInteger posAtGlobalExtraTab;

@property (nonatomic, strong) UPMarketIndexKlineIndexFetchLogic *klineDataFetchLogic;
@property (nonatomic, strong) UPMarketIndexMinIndexFetchLogic *rtMinDataFetchLogic;

@property (nonatomic, assign) BOOL showLongPress;

@end

@implementation UPMarket2SDIContentController {
    MASConstraint *_lastViewConstraint;
}

-(void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = UIColor.clearColor;
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(stockSettingChanged:)
                                                 name:UPMarket2StockSettingStateChangedNotificationName
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(historyMinuteDisableStatusNotify:)
                                                 name:kUPMarketUIHistoryMinuteNotificationKey
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(bottomSwitchToIndex:)
                                                 name:@"stockDetailBottomSwitchToIndex"
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(viewWillAppear:)
                                                 name:@"indexAndTemplateListUpdate"
                                               object:nil];
    
    if (self.trend.length) {
        if ([self.trend isEqualToString:UPRouterMarketStockTrendMinute]) {
            [[UPMarket2Preference sharedManager].tradeViewTabIndex setValue:@(0) forKey:@"chartTabIndex"];
        }
        
        if ([self.trend isEqualToString:UPRouterMarketStockTrendDay]) {
            [[UPMarket2Preference sharedManager].tradeViewTabIndex setValue:@(1) forKey:@"chartTabIndex"];
        }
    }
}



- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
//    if ([self.trend isEqualToString:UPRouterMarketStockTrendDay]) {
//        
//        // 指定K线图主副图索引
//        if ([self.currentChartView isKindOfClass:UPMarket2SDIKLineChartView.class] &&
//            [UPMarket2StockSettingStateManager cfqState] != UPMarket2StockSettingKLineCFQStateAfter) {
//            
//            if (IsValidateString(self.mainIndex) && [UPMarket2StockSettingStateManager isNotSaveKlineIndex:YES])
//            {
//                NSArray *indexArr = [self.currentChartView.indexHost getIndexArray:YES position:0 ];
//                if ([self.mainIndex integerValue] < indexArr.count) {
//                    NSUInteger indexId = [[indexArr objectAtIndex:[self.mainIndex integerValue]] integerValue];
//                    [UPMarket2StockSettingStateManager updateKlineIndexModeInNotSave:indexId isMajorIndex:YES index:0];
//                    [self.currentChartView.indexHost switchIndex:YES position:0 indexID:indexId];
//                }
//            }
//            
//            if (IsValidateString(self.viceIndex) && [UPMarket2StockSettingStateManager isNotSaveKlineIndex:NO]) {
//                NSArray *indexArr = [self.currentChartView.indexHost getIndexArray:NO position:0];
//                if ([self.viceIndex integerValue] < indexArr.count) {
//                    NSUInteger indexId = [[indexArr objectAtIndex:[self.viceIndex integerValue]] integerValue];
//                    [UPMarket2StockSettingStateManager updateKlineIndexModeInNotSave:indexId isMajorIndex:NO index:0];
//                    [self.currentChartView.indexHost switchIndex:NO position:0 indexID:indexId];
//                }
//            }
//        }
//    }
    
    self.headerView.stockModel = self.stockModel;
    [self.currentChartView viewWillAppear];
    [self.basicDataView viewWillAppear];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [self.currentChartView viewWillDisappear];
    
    [self.basicDataView viewWillDisappear];

    [self.bottomTradeView viewWillDisappear];
    
    [self extraViewWillDisappear:self.currentExtraView];
    
    [UPToastView cleanAll];
}

-(void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [UPThirdStatistics trackPageBegin:@"APP_Page_HQ_GGFS"];
    
    [self checkGuideView];
    
    [self showAlertIfNecessary:self.stockModel];
    
    [self updateTabViewIndex];
    
    [self.currentChartView viewDidAppear];
    
    if (self.bottomTradeView.superview) {
        [self.bottomTradeView viewDidAppear];
    }
    
    // 旧逻辑兼容
    [self extraViewDidAppear:self.currentExtraView];
    
    if ([self.currentChartView isKindOfClass:[UPMarket2SDIMinuteChartView class]]) {
        UPMarket2SDIMinuteChartView * chartView = (UPMarket2SDIMinuteChartView*)self.currentChartView;
        if (chartView.dayNum == 1) {
            UPHqStockHq *maskSelectStockHq = UPMarket2Preference.sharedManager.maskSelectStockHqDic[self.stockModel.stockCode];
            if (maskSelectStockHq) {
                [chartView maskStock:maskSelectStockHq];
            }
        }
    }
}

-(void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [UPThirdStatistics trackPageEnd:@"APP_Page_HQ_GGFS"];
    
    [self.klineDataFetchLogic clear];
    self.klineDataFetchLogic = nil;
    [self.rtMinDataFetchLogic clear];
    self.rtMinDataFetchLogic = nil;
    
    [self exitHistoryMinute];
    // 旧逻辑兼容
    [self extraViewDidDisappear:self.currentExtraView];
    
    [self.currentChartView viewDidDisappear];
    
    //补丁:在stockSettingChanged中会调用UPOptionalSDIBaseChartView.updateSettingChange
    //其中会重新创建所有cacheView的fetcher并start,这样在退出View时,也需要同步的将对应的featch进行停止
    //否则会导致请求没有停止的问题.
    for (UIView *view in self.viewCache.allValues) {
        if ([view isKindOfClass:UPMarket2SDIBaseChartView.class]) {
            [((UPMarket2SDIBaseChartView *)view) viewDidDisappear];
        }
    }

}

- (void)refreshCurrentChartView {
    if (self.currentChartView) {
        [self.currentChartView viewDidAppear];
    }
}

- (void)dealloc {
    if (!self.isLandscape) {
        UPMarket2Preference.sharedManager.maskSelectStockHqDic = nil;
    }
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

-(void)contentViewDidReuse:(UPHqStockHq *)stockHq {
    [super contentViewDidReuse:stockHq];
    
    self.currentChartView.displayScale = 1;

    self.isLandscape ? [self.landscapeBasicDataView clearData] : [self.basicDataView clearData];
    
    for (UIView *view in self.viewCache.allValues) {
        if ([view isKindOfClass:UPMarket2SDIBaseChartView.class]) {
            [((UPMarket2SDIBaseChartView *)view) clearData];
        }
        
        if ([view isKindOfClass:UPMarket2SDIBaseExtraView.class]) {
            [((UPMarket2SDIBaseExtraView *)view) clearData];
        }
    }
    
    self.stockBasicDataView = nil;
}

-(void)saveContentViewState:(NSCoder *)coder {
    // 保存滚动位置, 选中状态等
}

-(void)restoreContentViewState:(NSCoder *)coder {
    // 还原滚动位置, 选中状态等
}

- (void)networkStateDidChange:(BOOL)available {
    if (!available) {
        return;
    }
    
    if (self.sceneView.superview) {
        [self.sceneView requestData];
    }
    
    [self initIndexStatisticView:self.stockModel];
}

- (void)contentViewStockHqDidUpdate:(UPHqStockHq *)stockHq {
    [super contentViewStockHqDidUpdate:stockHq];
    // K线长按长按状态下不刷线盘口数据
    self.headerView.stockHq = stockHq;
    
    if (self.isLandscape) {
        self.landscapeBasicDataView.stockHq = stockHq;
    } else {
        self.basicDataView.stockHq = stockHq;
    }
    
    if (self.stockBasicDataView.superview) {
        self.stockBasicDataView.stockHq = stockHq;
    }
    
    if (!self.isLandscape && self.chartViewPopupView.isShowing &&
        [self.chartViewPopupView.contentView isKindOfClass:UPMarket2SDIExtendBasicDataView.class]) {
        UPMarket2SDIExtendBasicDataView *extendBasicView = (UPMarket2SDIExtendBasicDataView *)self.chartViewPopupView.contentView;
        extendBasicView.stockHq = stockHq;
    }
    
    if (self.bottomTradeView.superview) {
        self.bottomTradeView.stockHq = stockHq;
    }
    
    if (self.statisticView.superview) {
        self.statisticView.stockHq = stockHq;
    }
    
    self.currentChartView.stockHq = stockHq;
    
    if ([self.currentExtraView isKindOfClass:UPMarket2SDIBaseView.class]) {
        UPMarket2SDIBaseView *extraView = (UPMarket2SDIBaseView *)self.currentExtraView;
        extraView.stockHq = stockHq;
    }
    if (!self.isLandscape) {
        [self.scrollView up_endPullRefresh];
    }
}

-(void)contentViewStockL2DidUpdate:(BOOL)isStockL2 {
    self.stockModel = self.stockModel;
}

// MARK: setupViews

-(void)setupViews {
    [self.view addSubview:self.headerView];
    
    [self.view addSubview:self.scrollView];
    
    [self.scrollView addSubview:self.bgView];
    [self.scrollView addSubview:self.topContainerView];
    [self.scrollView addSubview:self.bottomContainerView];
    
    [self.topContainerView addSubview:self.basicDataView];
    
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.backgroundColor = UIColor.up_titleBarBgColor;
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.alignment = UIStackViewAlignmentCenter;
    stackView.distribution = UIStackViewDistributionFill;
    stackView.spacing = 20;
    [stackView addArrangedSubview:self.chartsTabView];
    [stackView addArrangedSubview:self.indexSettingButton];
    [self.topContainerView addSubview:stackView];
    self.stackView = stackView;
    [self.chartsTabView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(stackView);
    }];
    UIView *sepline = [[UIView alloc] init];
    sepline.backgroundColor = UIColor.up_dividerColor;
    [stackView addArrangedSubview:sepline];
    [sepline mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@1);
    }];
    [stackView setCustomSpacing:14 afterView:sepline];
    
    
    [self.bottomContainerView addSubview:self.extraTabView];
}

-(void)setupViewsForLandscape {
    // 横屏不添加ScrollView
    [self.view addSubview:self.topContainerView];
    
    self.topContainerView.backgroundColor = UIColor.up_contentBgColor;
    
    [self.topContainerView addSubview:self.landscapeBasicDataView];
    
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.backgroundColor = UIColor.up_titleBarBgColor;
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.alignment = UIStackViewAlignmentCenter;
    stackView.distribution = UIStackViewDistributionFill;
    stackView.spacing = 20;
    [stackView addArrangedSubview:self.chartsTabView];
    [stackView addArrangedSubview:self.indexSettingButton];
    [self.topContainerView addSubview:stackView];
    self.stackView = stackView;
    [self.chartsTabView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(stackView);
    }];
    UIView *sepline = [[UIView alloc] init];
    sepline.backgroundColor = UIColor.whiteColor;
    [stackView addArrangedSubview:sepline];
    [sepline mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@1);
    }];
    [stackView setCustomSpacing:14 afterView:sepline];

    [self.topContainerView addSubview:stackView];
    
    [self initLandscapeBottomToolView:self.stockModel];
}

// MARK: layoutViews

-(void)layoutViews {
    self.headerView.frame = CGRectMake(0, 0, self.view.bounds.size.width, self.up_safeAreaInsets.top + 44);
    
    [self.scrollView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.headerView.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
    
    [self.bgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.scrollView);
        // 加上历史分时的高度
        make.height.equalTo(self.scrollView).offset(281);
    }];
    
    [self.topContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.centerX.equalTo(self.scrollView);
    }];
    
    [self.bottomContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.centerX.bottom.equalTo(self.scrollView);
        make.top.equalTo(self.topContainerView.mas_bottom);
    }];
    
    [self.basicDataView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.topContainerView);
    }];
    
    [self.indexSettingButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@-15);
        make.centerY.equalTo(self.chartsTabView);
    }];
    
    [self.stackView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.basicDataView);
        make.top.equalTo(self.basicDataView.mas_bottom).offset(6);
        make.height.equalTo(@40);
    }];
    
    UIView *lastView = nil;
    if(self.currentChartView.superview) {
        [self.currentChartView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.topContainerView);
            make.top.equalTo(self.stackView.mas_bottom);
            make.height.equalTo(@364).priorityLow();
        }];
        lastView = self.currentChartView;
    }
    
    if (self.statisticView.superview) {
        [self.statisticView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.topContainerView);
            make.top.equalTo(lastView.mas_bottom).offset(6);
            make.height.equalTo(@100);
        }];
        lastView = self.statisticView;
    }
    
    if (self.bottomTradeView.superview) {
        [self.bottomTradeView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.topContainerView);
            make.top.equalTo(self.currentChartView.mas_bottom).offset(6);
            make.height.greaterThanOrEqualTo(@160);
        }];
        lastView = self.bottomTradeView;
    }
    
    if (self.sceneView.superview) {
        [self.sceneView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.topContainerView);
            make.top.equalTo(lastView.mas_bottom);
            make.height.equalTo(@30);
        }];
        
        lastView = self.sceneView;
    }
    
    if (lastView) {
        [_lastViewConstraint deactivate];
        
        [lastView mas_makeConstraints:^(MASConstraintMaker *make) {
            _lastViewConstraint = make.bottom.equalTo(self.topContainerView).offset(-6).priorityHigh();
        }];
    }
    
    if (self.extraTabView.superview) {
        [self.extraTabView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.basicDataView);
            make.top.equalTo(self.bottomContainerView);
            make.height.equalTo(@(UPMarket2SDIExtraTabViewHeight));
        }];
        
        if (self.etRightCoverLayer) {
            [self.etRightCoverLayer removeFromSuperlayer];
            self.etRightCoverLayer = nil;
        }
        [self.etRightCover.layer addSublayer:self.etRightCoverLayer];
        
        CGFloat extraViewHeight = self.view.up_height - (self.up_safeAreaInsets.top + 44) - UPMarket2SDIExtraTabViewHeight;
        [self.currentExtraView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.bottomContainerView);
            make.top.equalTo(self.extraTabView.mas_bottom);
            make.bottom.equalTo(self.bottomContainerView);
            make.height.equalTo(@(extraViewHeight));
        }];
    }
    
    static CGFloat topContainerViewHeight = 0;
    if (topContainerViewHeight != self.topContainerView.up_height) {
        topContainerViewHeight = self.topContainerView.up_height;
        [self postScrollNotification:self.scrollView];
    }
}

-(void)layoutViewsForLandscape {
    [self.topContainerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    [self.landscapeBasicDataView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.topContainerView);
        make.height.equalTo(@57);
    }];
    [self.stackView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.topContainerView);
        make.right.equalTo(self.topContainerView).priorityLow();
        make.height.equalTo(@35);
        make.top.equalTo(self.landscapeBasicDataView.mas_bottom);
    }];
    if(self.currentChartView.superview) {
        [self.currentChartView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.topContainerView);
            make.top.equalTo(self.stackView.mas_bottom);
            make.bottom.equalTo(self.topContainerView);
        }];
    }
}

// MARK: setupViewsContent

-(void)setupViewsContent {
    // 配置个股图表tab
    {
        if (self.isLandscape) {
            self.chartsTabView.angleDirection = UPTabSubViewAngleDirectionBottom;
        } else {
            self.chartsTabView.angleDirection = UPTabSubViewAngleDirectionTop;
        }
        self.chartsTabView.tabItems = [self buildChartsTabViewItems];
        
        NSNumber *chartTabIndexValue = [[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartTabIndexWillSet"];
        NSNumber *chartSubTabIndexValue = [[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartSubTabIndexWillSet"];
        NSInteger chartTabIndex = chartTabIndexValue.integerValue;
        NSInteger chartSubTabIndex = chartSubTabIndexValue.integerValue;
        
        [self.chartsTabView setSelectedItemIndex:chartTabIndex subItemIndex:chartSubTabIndex];
    }
    
    // 配置个股底部tab
    {
        [self.extraTabView removeFromSuperview];
        [self.currentExtraView removeFromSuperview];
        
        NSArray *array = [self buildExtraTabViewItems];
        if (IsValidateArray(array)) {
            [self.bottomContainerView addSubview:self.extraTabView];
            [self.bottomContainerView addSubview:self.currentExtraView];
            
            self.extraTabView.tabItems = array;

            [self resetGlobalExtraTabIfNecessary];
            NSUInteger extraTabIndex = [UPMarket2SDIContentController.sGlobalExtraTab[self.posAtGlobalExtraTab] integerValue];
            NSUInteger maxCount = self.extraTabView.tabItems.count;
            if (extraTabIndex >= maxCount) {
                extraTabIndex = maxCount > 0 ? maxCount - 1 : 0;
            }
            [self.extraTabView setSelectedItemIndex:extraTabIndex subItemIndex:0];
        }
    }
}

// MARK: Notification
- (void)stockSettingChanged:(NSNotification *)notification {
    
    for (UIView *view in self.viewCache.allValues) {
        if ([view isKindOfClass:UPMarket2SDIBaseChartView.class]) {
            UPMarket2SDIBaseChartView *chartView = (UPMarket2SDIBaseChartView *)view;
            [chartView updateSettingChange];
            chartView.stockHq = self.stockHq;
            chartView.stockModel = self.stockModel;
        }
    }
    
    self.chartsTabView.selectedIndex = self.chartsTabView.selectedIndex;
    [self initBottomTradeView:self.stockModel];
}

- (void)stockViewsUpdate:(NSNotification *)notification {
    
    for (UIView *view in self.viewCache.allValues) {
        if ([view isKindOfClass:UPMarket2SDIBaseChartView.class]) {
            [((UPMarket2SDIBaseChartView *)view) updateWhileStockViewsChange];
        }
    }
    
//    [self initBottomTradeView:self.stockModel];
}

- (void)historyMinuteDisableStatusNotify:(NSNotification *)notification {
    
    NSDictionary *disableStatusDic = notification.userInfo;
    
    BOOL isLeftDisable = [disableStatusDic[@"isLeftDisable"] boolValue];
    BOOL isRightDisable = [disableStatusDic[@"isRightDisable"] boolValue];
    
    self.isLeftDisable = isLeftDisable;
    self.isRightDisable = isRightDisable;
    
    if (self.historyMinuteView && self.historyMinuteView.superview) {
        self.historyMinuteView.isLeftDisable = isLeftDisable;
        self.historyMinuteView.isRightDisable = isRightDisable;
    }
}

-(void)notifyStockListHidden:(BOOL)hidden {
    if (self.shouldShowStockList == NO) {
        [self.landscapeBasicDataView updateListButton:YES];
    } else {
        [self.landscapeBasicDataView updateListButton:hidden];
    }
    self.landscapeBasicDataView.shouldShowStockList = self.shouldShowStockList;
}

- (void)postScrollNotification:(UIScrollView *)scrollView {
    if (self.isLandscape) {
        return;
    }
    
    self.scrollView.bounces = scrollView.contentOffset.y <= 0;
    
    BOOL extraViewScrollEnable = scrollView.contentOffset.y >= self.topContainerView.up_height;
    
    [[NSNotificationCenter defaultCenter] postNotificationName:UPMarket2SDIScrollViewOffsetChangedNotification object:@(extraViewScrollEnable) userInfo:nil];
}

- (void)bottomSwitchToIndex:(NSNotification *)notification {
    NSDictionary *dic = notification.userInfo;
    if (dic) {
        NSInteger index = [dic[@"index"] integerValue];
        [self.extraTabView setSelectedItemIndex:index subItemIndex:0];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            NSInteger subIndex = [dic[@"subIndex"] integerValue];
            if ([self.currentExtraView isKindOfClass:NSClassFromString(@"FMStockDetailFundView")]) {
                [self.currentExtraView setValue:@(subIndex) forKey:@"index"];
            }
        });
    }
}

// MARK: UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    
    [self postScrollNotification:scrollView];
    
    if (scrollView.contentOffset.y > self.basicDataView.up_height) {
        [self.headerView hideDefault];
    } else {
        [self.headerView showDefault];
    }
    
    [self.currentChartView superScrolling];
}

// MARK: UPTabViewDelegate

-(void)tabView:(UPTabView *)tabView didSelectItem:(UPTabViewItem *)item {
    if(tabView == self.chartsTabView) {
        if (self.currentChartView.isHistoryMinute) {
            [self exitHistoryMinute];
        }
        
        [[UPMarket2Preference sharedManager].tradeViewTabIndex setValue:@(tabView.selectedIndex) forKey:@"chartTabIndexWillSet"];
        [[UPMarket2Preference sharedManager].tradeViewTabIndex setValue:@(tabView.selectedSubIndex) forKey:@"chartSubTabIndexWillSet"];
        [self showChartsTabWithTag:item.tag];
        [[UPMarket2Preference sharedManager].tradeViewTabIndex setValue:@(tabView.selectedIndex) forKey:@"chartTabIndex"];
        [[UPMarket2Preference sharedManager].tradeViewTabIndex setValue:@(tabView.selectedSubIndex) forKey:@"chartSubTabIndex"];
        
    } else if(tabView == self.extraTabView) {
        [self showExtraTabWithTag:item.tag];
        UPMarket2SDIContentController.sGlobalExtraTab[self.posAtGlobalExtraTab] = @(tabView.selectedIndex);
    }
}

// MARK: UPMarket2SDIBasicDataViewDelegate

- (void)sdiBasicDataViewAIAlarmButtonClicked:(UPMarket2SDIBasicDataView *)sdiBasicDataView {
    
    if (!IsValidateString(self.stockHq.code)) return;
    
    NSString *urlStr = [UPURLAIAlarmStrategy up_buildURLWithQueryParams:@{
        @"stockCode" : self.stockHq.code,
        @"market"    : [@(self.stockHq.setCode) stringValue],
        @"up_immersive" : [@(1) stringValue],
    }];
    
    [UPRouterUtil goAfterUserLogin:urlStr];
}

-(void)sdiBasicDataViewMoreDataButtonClicked:(UPMarket2SDIBasicDataView *)sdiBasicDataView {
    
    WeakSelf(weakSelf)
    if (!self.stockBasicDataView) {
        self.stockBasicDataView = [[UPMarket2SDIExtendBasicDataView alloc] initWithStockModel:self.stockModel];
    }
    
    self.stockBasicDataView.hideBlock = ^{
        [weakSelf.chartViewPopupView hide];
    };
    
    self.stockBasicDataView.stockHq = sdiBasicDataView.stockHq;
    self.stockBasicDataView.mainStockData = sdiBasicDataView.relatedMainStockData;
    self.chartViewPopupView.contentView = self.stockBasicDataView;
    self.chartViewPopupView.contentSize = self.outerContainerViewController.view.frame.size;
    
    [self.chartViewPopupView showInView:self.outerContainerViewController.view atPosition:CGPointZero];
}

-(void)sdiBasicDataViewDealVolIconClicked:(UPMarket2SDIBasicDataView *)sdiBasicDataView {
    // 弹出债券现券提示框
    self.dealVolPopupView.contentView = self.dealVolContent;
    self.dealVolPopupView.contentSize = self.outerContainerViewController.view.frame.size;
    [self.dealVolPopupView showInView:self.outerContainerViewController.view atPosition:CGPointZero];
}

-(void)sdiBasicDataViewOptionalButtonClicked:(UPMarket2SDIBasicDataView *)sdiBasicDataView {
}

-(void)sdiBasicDataViewRelatedStockHq:(UPHqStockHq *)mainStockData relatedType:(UPMarket2SDIRelatedStockType)relatedType {
    if (self.stockBasicDataView.superview) {
        self.stockBasicDataView.mainStockData = mainStockData;
    }
}

- (BOOL)sdiBasicDataViewSupportUpdateWhenLongPress {
    return self.currentChartView.supportUpdateWhenLongPress;
}

- (BOOL)sdiBasicDataViewCanShowAfterTradeKCBLabel {
    return self.currentChartView.canShowAfterTradeKCBLabel;
}

// MARK: UPMarket2SDILandscapeBasicDataViewDelegate

-(void)sdiLandscapeBasicDataViewGoPrevButtonClicked:(UPMarket2SDILandscapeBasicDataView *)sdiLandscapeBasicDataView {
    [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_ZQH"];
    [self goPrevContentController];
}

-(void)sdiLandscapeBasicDataViewGoNextButtonClicked:(UPMarket2SDILandscapeBasicDataView *)sdiLandscapeBasicDataView {
    [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_YQH"];
    [self goNextContentController];
}

- (void)sdiLandscapeBasicDataView:(UPMarket2SDILandscapeBasicDataView *)sdiLandscapeBasicDataView stockListButtonClicked:(BOOL)hidden {
    // 盘口中的列表按钮隐藏则股票列表显示，否则反之
    [self updateStockList:!hidden];
}

-(void)sdiLandscapeBasicDataViewCloseButtonClicked:(UPMarket2SDILandscapeBasicDataView *)sdiLandscapeBasicDataView {
    [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_GB"];
    [self exitContentController];
}

// MARK: UPMarket2SDIHeaderViewDelegate

- (void)sdiHeaderViewBackButtonClicked:(UPMarket2SDIHeaderView *)sdiHeaderView {
    [self exitContentController];
}

- (void)sdiHeaderViewSearchButtonClicked:(UPMarket2SDIHeaderView *)sdiHeaderView {
    [self enterSearch];
}

- (void)sdiHeaderViewStockTypeViewClicked:(UPMarket2SDIHeaderView *)sdiHeaderView tagArray:(nonnull NSArray *)tagArray {
    [self showStockTypeView:tagArray];
}

- (void)sdiHeaderViewPreButtonClicked:(UPMarket2SDIHeaderView *)sdiHeaderView {
    [self goPrevContentController];
}

- (void)sdiHeaderViewNextButtonClicked:(UPMarket2SDIHeaderView *)sdiHeaderView {
    [self goNextContentController];
}


// MARK: UPMarket2SDIStockMaskViewDelegate
- (void)sdiStockMaskViewClickCustom {
    UPStockPickerController *picker = [[UPStockPickerController alloc] init];
    picker.delegate = self;
    picker.filter = [NSString stringWithFormat:@"setCode == %zd OR setCode == %zd OR setCode == %zd", UPMarketSetCodeSH, UPMarketSetCodeSZ, UPMarketSetCodeBJ];
    [self.navigationController pushViewController:picker animated:YES];
}

- (void)sdiStockMaskViewClickStockHq:(UPHqStockHq *)stockHq {
    [self.currentChartView maskStock:stockHq];
}

// MARK: UPMarketSDIOptionalToastDelegate
- (void)sdiOptionalToastEditGroupClicked {
    [self editOptionalGroup];
}

// MARK: UPStockPickerControllerDelegate
- (void)stockPickerController:(UPStockPickerController *)picker didFinishPickingWithResults:(NSArray<UPMarketCodeMatchInfo *> *)results {
    
    if (!IsValidateArray(results)) {
        return;
    }
    
    UPMarketCodeMatchInfo *result = results.firstObject;
    
    UPHqStockHq *stockHq = [[UPHqStockHq alloc] init];
    stockHq.name = result.name;
    stockHq.setCode = result.setCode;
    stockHq.code = result.code;
    
    [self.currentChartView maskStock:stockHq];
    if ([self.chartViewPopupView.contentView isKindOfClass:[UPMarket2SDIStockMaskView class]]) {
        UPMarket2SDIStockMaskView * view = (UPMarket2SDIStockMaskView*)self.chartViewPopupView.contentView;
        view.hideBlock();
    }
}

// MARK: - UPMarket2SDISceneViewDelegate
- (void)sdiSceneViewRequestDataFailed {
    if (self.isLandscape) return;
    
    [self.sceneView removeFromSuperview];
    
    if (self.viewLoaded) {
        [self.view setNeedsLayout];
    }
}

// MARK: Private

- (void)updateStockList:(BOOL)hidden {
    __strong id<UPMarket2SDIBaseContentControllerDelegate> delegate = self.delegate;

    if(delegate && [delegate respondsToSelector:@selector(sdiContentController:updateStockListView:)]) {
        [delegate sdiContentController:self updateStockListView:hidden];
    }
}

- (void)dealVolPopupViewClick {
    [self.dealVolPopupView hide];
}

- (void)resetGlobalExtraTabIfNecessary {
    if (!self.tab.length) {
        return;
    }
    
    UPMarket2SDIExtraTabItemTag extraTabItemTag = NSNotFound;
    if ([self.tab isEqualToString:UPRouterMarketStockTabDynamic]) {
        extraTabItemTag = UPMarket2SDIExtraTabItemTagSubjectChange;
    } else if ([self.tab isEqualToString:UPRouterMarketStockTabMoney]) {
        extraTabItemTag = UPMarket2SDIExtraTabItemTagFund;
    }  else if ([self.tab isEqualToString:UPRouterMarketStockTabNews]) {
        extraTabItemTag = UPMarket2SDIExtraTabItemTagNews;
    }  else if ([self.tab isEqualToString:UPRouterMarketStockTabNotice]) {
        extraTabItemTag = UPMarket2SDIExtraTabItemTagNotice;
    }  else if ([self.tab isEqualToString:UPRouterMarketStockTabF10]) {
        extraTabItemTag = UPMarket2SDIExtraTabItemTagF10;
    }
    
    if (extraTabItemTag == NSNotFound) {
        return;
    }
    
    self.tab = nil;
    
    [self.extraTabView.tabItems enumerateObjectsUsingBlock:^(UPTabViewItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.tag == extraTabItemTag) {
            UPMarket2SDIContentController.sGlobalExtraTab[self.posAtGlobalExtraTab] = @(idx);
            
            *stop = YES;
        }
    }];
    
}

- (void)checkGuideView {
    BOOL hasShowSwitchGuide = [[NSUserDefaults standardUserDefaults] boolForKey:UP_STOCKS_SWITCH_GUIDE_POP_SHOW];
    if (!hasShowSwitchGuide) {
        [self showStockSwitchGuide];
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:UP_STOCKS_SWITCH_GUIDE_POP_SHOW];
    }
}

- (void)showAlertIfNecessary:(UPMarketUIBaseModel *)stockModel {
    if ((stockModel.stockSetCode == UPMarketSetCodeHK || stockModel.stockSetCode == UPMarketSetCodeHKI) && !UPMarket2Config.sharedInstance.hkStockHqDisclaimed) {
        UPAlertView * alertView = [[UPAlertView alloc] init];
        
        alertView.title = @"免责声明";
        alertView.message = @"根据香港交易所规定，免费行情将有至少15分钟的延迟";
        
        [alertView addActionWithTitle:@"我知道了" titleColor:UIColor.up_brandColor handler:nil];
        [alertView show];
        
        UPMarket2Config.sharedInstance.hkStockHqDisclaimed = YES;
    } else if (stockModel.isStockOfMarketUS && !UPMarket2Config.sharedInstance.usStockHqExplained) {
        UPAlertView * alertView = [[UPAlertView alloc] init];
        
        alertView.title = @"免责声明";
        alertView.message = @"根据交易所规定，免费行情将有至少15分钟的延迟";
        
        [alertView addActionWithTitle:@"我知道了" titleColor:UIColor.up_brandColor handler:nil];
        [alertView show];
        
        UPMarket2Config.sharedInstance.usStockHqExplained = YES;
    }
}

- (void)showStockSwitchGuide {
    UIImageView *guideView = [[UIImageView alloc] initWithImage:UPTImg(@"个股/个股-切换-引导")];
    UPPopupView *view = [UPPopupView new];
    view.contentView = guideView;
    CGSize size = guideView.frame.size;
    view.contentSize = size;
    view.backgroundColor = [UIColor up_colorFromHexString:@"#99111111"];

    [view showInView:self.outerContainerViewController.view atPosition:CGPointMake((self.view.up_width - size.width)/2, (self.view.up_height - size.height)/2)];
}


- (void)enterSearch {
    [FMUPDataTool gotoSearch];
}

- (void)onRefreshScrollView {
    [self requestRefreshData];
}

-(void)endRefreshData {
    [self.scrollView up_endPullRefresh];
}

- (void)initBottomTradeView:(UPMarketUIBaseModel *)stockModel {
    [self.bottomTradeView removeFromSuperview];
    
    BOOL isScaled = [UPThemeManager currentFontMode] == UPFontModeGlobal || [UPThemeManager currentFontMode] == UPFontModeModuleHq;
    
    if (UPMarket2StockSettingStateManager.wdwzPosition == UPMarket2StockSettingMinuteWDWZPositionRight && !isScaled) {
        return;
    }
    
    if (self.isLandscape || ![self canShowTradeView:stockModel]) {
        return;
    }
    
    UPMarket2SDIBottomTradeView *bottomTradeView = [[UPMarket2SDIBottomTradeView alloc] initWithStockModel:stockModel];
    bottomTradeView.stockHq = self.stockHq;
    
    [self.topContainerView addSubview:bottomTradeView];
    
    self.bottomTradeView = bottomTradeView;
}

- (void)initSceneView:(UPMarketUIBaseModel *)stockModel {
//    if (self.isLandscape || ![stockModel isStockOfMarketHSA]) {
//        return;
//    }
//
//    [self.sceneView removeFromSuperview];
//
//    [self.topContainerView addSubview:self.sceneView];
//
//    self.sceneView.stockModel = stockModel;
//
//    [self.sceneView requestData];
}

- (BOOL)isSupportTrade:(UPMarketUIBaseModel *)stockModel {
    return [stockModel isStockOfMarketHS] || (stockModel.stockSetCode == UPMarketSetCodeBJ && stockModel.stockCategory != UPMarketStockCategory_INDEX) || stockModel.stockSetCode == UPMarketSetCodeBH || stockModel.stockCategory == UPMarketStockCategory_FUND || (stockModel.stockCategory == UPMarketStockCategory_BOND && !stockModel.isStockOfZQNHG) || [stockModel isStockOfGGT] || (stockModel.stockCategory == UPMarketStockCategory_XSB && stockModel.stockSubCategory != UPMarketStockSubCategory_XSB_ZS);
}

- (void)initLandscapeBottomToolView:(UPMarketUIBaseModel *)stockModel {
    if (!self.isLandscape || !stockModel.isUpdated || (![self isSupportTrade:stockModel] && !stockModel.isStockOfZQNHG) || !self.topContainerView.superview) {
        [self.landscapeBottomToolView removeFromSuperview];
        self.landscapeBottomToolView = nil;
        return;
    }
    
    [self.landscapeBottomToolView removeFromSuperview];
    self.landscapeBottomToolView = nil;
    
    [self.topContainerView addSubview:self.landscapeBottomToolView];
    
    [self.landscapeBottomToolView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.topContainerView);
        make.width.equalTo(@210);
        make.height.centerY.equalTo(self.chartsTabView);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.topContainerView);
    }];
}

- (void)initIndexStatisticView:(UPMarketUIBaseModel *)stockModel {
    if (self.isLandscape || ![self isNetworkAvailable]) {
        return;
    }
    
    [self.statisticView removeFromSuperview];
    
    BOOL bjsIndex = [UPMarketCategoryUtil isBJMarket:stockModel.stockSetCode] && stockModel.stockCategory == UPMarketStockCategory_INDEX;
    if (bjsIndex || [UPMarketCategoryUtil isHSIndex:stockModel.stockSetCode category:stockModel.stockCategory] || [UPMarketCategoryUtil isBlock:stockModel.stockCategory] || (stockModel.stockCategory == FMMarketStockCategory_BLOCK)) {
        self.statisticView = [UPMarket2SDIIndexStatisticView new];
        [self.topContainerView addSubview:self.statisticView];
    }
}

- (BOOL)canShowTradeView:(UPMarketUIBaseModel *)stockModel {
    if (stockModel.stockCategory == UPMarketStockCategory_INDEX) {  //指数分类不显示五档明细
        return NO;
    }
    return (([stockModel isStockOfMarketHSAGroup] || [stockModel isStockOfMarketHSB])
            || stockModel.stockSetCode == UPMarketSetCodeBJ
            || stockModel.isStockOfGGT || stockModel.isStockOfMarketHK
            || stockModel.stockCategory == UPMarketStockCategory_BOND
            || stockModel.stockCategory == UPMarketStockCategory_FUND
            || stockModel.stockCategory == UPMarketStockCategory_FUTURE
            || stockModel.stockCategory == UPMarketStockCategory_METAL
            || stockModel.stockCategory == UPMarketStockCategory_OPTION
            || [stockModel isStockOfMarketUS]
            || (stockModel.stockSetCode == UPMarketSetCodeXSB && stockModel.stockSubCategory != UPMarketStockSubCategory_XSB_ZS));
}

- (void)updateLongPressModel:(id)data {
    // data为nil表示取消长按状态
    self.showLongPress = data != nil;

    if ([self.currentChartView isKindOfClass:UPMarket2SDIKLineChartView.class]) {
        UPMarket2SDIKLineChartView *chartView = (UPMarket2SDIKLineChartView *)self.currentChartView;
        
        chartView.model = data;
    }
    
    if (!data) {
        [self.longPressMaskView removeFromSuperview];
        self.longPressMaskView = nil;
        
        // 取消长按后,立刻刷新当前盘口数据
        [self contentViewStockHqDidUpdate:self.stockHq];
    } else {
        // 展示长按的遮罩视图
        if (self.currentChartView.canShowLongClickMaskView) {
            if (!self.longPressMaskView) {
                UPMarket2SDILongPressMaskView *longPressMaskView = [UPMarket2SDILongPressMaskView new];
                longPressMaskView.isLandscape = self.isLandscape;
                longPressMaskView.stockHq = self.stockHq;
                self.longPressMaskView = longPressMaskView;
                [self.topContainerView addSubview:self.longPressMaskView];
                
                    [self.longPressMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.left.right.equalTo(self.topContainerView);
                        make.height.centerY.equalTo(self.chartsTabView);
                    }];
            }
            
            self.longPressMaskView.model = data;
        } else {
            if (self.isLandscape) {
                [self.landscapeBasicDataView updateWithModel:data];
            } else {
                [self.basicDataView updateWithModel:data];
            }
        }
    }
}

- (void)showSelectIndexView:(UPMarket2SDISelectIndexModel *)model {
    WeakSelf(weakSelf)
    UPMarket2SDISelectIndexView *selectIndexView = [[UPMarket2SDISelectIndexView alloc] initWithIsLandscape:self.isLandscape];
    
    selectIndexView.model = model;
    selectIndexView.hideBlock = ^{
        [weakSelf.chartViewPopupView hide];
    };
    
    self.chartViewPopupView.contentView = selectIndexView;
    self.chartViewPopupView.contentSize = CGSizeMake(self.outerContainerViewController.view.up_width, self.outerContainerViewController.view.up_height - self.outerContainerViewController.view.up_safeAreaInsets.bottom);
    
    [self.chartViewPopupView showInView:self.outerContainerViewController.view atPosition:CGPointZero];
}

- (void)showMaskStockView {
    self.chartViewPopupView.contentView = self.stockMaskView;
    WeakSelf(weakSelf)
    self.stockMaskView.delegate = self;
    self.stockMaskView.stockModel = self.stockModel;
    self.stockMaskView.maskSelectStockHq = self.currentChartView.maskSelectStockHq;
    self.stockMaskView.hideBlock = ^{
        [weakSelf.chartViewPopupView hide];
    };
    
    self.chartViewPopupView.contentSize = self.outerContainerViewController.view.frame.size;
    [self.chartViewPopupView showInView:self.outerContainerViewController.view atPosition:CGPointZero];
}

- (void)showStockTypeView:(NSArray *)tagArray {
    WeakSelf(weakSelf)
    UPMarket2SDIStockTypeView *stockTypeView = [[UPMarket2SDIStockTypeView alloc] init];
    stockTypeView.stockModel = self.stockModel;
    stockTypeView.highlightArray = tagArray;
    
    stockTypeView.hideBlock = ^{
        [weakSelf.chartViewPopupView hide];
        
    };
    
    self.chartViewPopupView.contentView = stockTypeView;
    self.chartViewPopupView.contentSize = self.outerContainerViewController.view.frame.size;
    
    [self.chartViewPopupView showInView:self.outerContainerViewController.view atPosition:CGPointZero];
}

- (void)updateTabViewIndex {
    NSUInteger chartTabIndex = [[[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartTabIndex"] intValue];
    NSUInteger chartSubTabIndex = [[[UPMarket2Preference sharedManager].tradeViewTabIndex valueForKey:@"chartSubTabIndex"] intValue];
    NSUInteger extraTabIndex = [UPMarket2SDIContentController.sGlobalExtraTab[self.posAtGlobalExtraTab] integerValue];
    
    if (self.chartsTabView.selectedIndex != chartTabIndex || self.chartsTabView.selectedSubIndex != chartSubTabIndex) {
        [self.chartsTabView setSelectedItemIndex:chartTabIndex subItemIndex:chartSubTabIndex];
    }
    
//    if (self.extraTabView.selectedIndex != extraTabIndex) {
//        [self.extraTabView setSelectedIndex:extraTabIndex];
//    }
}

- (void)updateHistoryMinuteView:(UPMarketUIKlineModel *)model {
    self.historyMinuteView.model = model;
}

- (void)exitHistoryMinute {
    if (!self.historyMinuteView.superview) {
        return;
    }
    
    [self.historyMinuteView viewDidDisappear];
    [self.historyMinuteView removeFromSuperview];
    self.historyMinuteView = nil;
    
    self.currentChartView.isHistoryMinute = NO;
    [self.currentChartView exitCrossLineMode];
}

- (void)configHistoryMinuteView {
    WeakSelf(weakSelf)
    
    UPMarket2SDIHistoryMinuteView *historyMinuteView = [UPMarket2SDIHistoryMinuteView new];
    historyMinuteView.stockModel = self.stockModel;
    historyMinuteView.stockHq = self.stockHq;
    historyMinuteView.isLeftDisable = self.isLeftDisable;
    historyMinuteView.isRightDisable = self.isRightDisable;
    historyMinuteView.backgroundColor = UIColor.up_contentBgColor;
    
    historyMinuteView.historyMinuteButtonClickBlock = ^(UPMarket2SDIHistoryMinuteButtonTag tag) {
        if (tag == UPMarket2SDIHistoryMinuteButtonTagExit) {
            [weakSelf exitHistoryMinute];
            [weakSelf.scrollView setContentOffset:CGPointMake(0, 0) animated:YES];
        } else if (tag == UPMarket2SDIHistoryMinuteButtonTagGoBack) {
            if (!self.isNetworkAvailable) {
                [UPToastView show:@"网络连接失败"];
            }
            [weakSelf.currentChartView moveCrossLine:UPMarketUIMoveCrossLineTypeLeft];
        } else if (tag == UPMarket2SDIHistoryMinuteButtonTagGoForward) {
            if (!self.isNetworkAvailable) {
                [UPToastView show:@"网络连接失败"];
            }
            [weakSelf.currentChartView moveCrossLine:UPMarketUIMoveCrossLineTypeRight];
        }
    };
    
    historyMinuteView.historyMinuteChooseIndexClick = ^(UPMarket2SDISelectIndexModel * _Nonnull indexModel) {
        //点击指标名字
        [weakSelf showSelectIndexView:indexModel];
    };
    
    [self.outerContainerViewController.view addSubview:historyMinuteView];
    
    [historyMinuteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.outerContainerViewController.view);
        make.bottom.equalTo(self.outerContainerViewController.up_safeAreaLayoutGuideBottom);
        make.height.equalTo(@281);
    }];
    
    [historyMinuteView viewDidAppear];
    
    self.historyMinuteView = historyMinuteView;
}

- (void)regionDataUpdated:(NSArray<UPMarketUIKlineModel *> *)data {
    if (self.isLandscape) {
        [self.landscapeBasicDataView updateRegionData:data];
    } else {
        [self.basicDataView updateRegionData:data];
    }
}

- (void)hideRegion {
    [self.currentChartView endRegion];
}

- (void)clickQXModel:(UPMarketUIKlineModel *)klineModel qxData:(UPHqQXData *)qxData {
    
    WeakSelf(weakSelf)
    
    UPMarket2SDIQXDetailView *detailView = [[UPMarket2SDIQXDetailView alloc] init];
    detailView.hideBlock = ^{
        [weakSelf.chartViewPopupView hide];
    };
    
    [detailView setSelectedQXData:klineModel.qxChgData qxData:qxData];
    
    self.chartViewPopupView.contentView = detailView;
    self.chartViewPopupView.contentSize = self.outerContainerViewController.view.frame.size;
    [self.chartViewPopupView showInView:self.outerContainerViewController.view atPosition:CGPointZero];
}

- (void)scrollTabViewToTop {
    CGPoint originPoint = [self.chartsTabView convertPoint:CGPointZero toView:self.headerView];
    
    CGPoint offset = CGPointMake(0, self.scrollView.contentOffset.y + originPoint.y - CGRectGetMaxY(self.headerView.frame));
    
    [self.scrollView setContentOffset:offset animated:YES];
}

- (void)trackEvent:(int)tag {
    NSString *eventString = nil;
    BOOL hasParam = NO;
    
    switch (tag) {
        case UPMarket2SDIChartTabItemTagDailyMinute: {
            eventString = @"APP_HQ_GGFS_FS";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagFiveDayMinute: {
            eventString = @"APP_HQ_GGFS_WR";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagDailyKLine: {
            eventString = @"APP_HQ_GGFS_RK";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagWeeklyKLine: {
            eventString = @"APP_HQ_GGFS_ZK";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMonthlyKLine: {
            eventString = @"APP_HQ_GGFS_YK";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagSeasonKLine: {
            eventString = @"APP_HQ_GGFS_JK";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagYearKLine: {
            eventString = @"APP_HQ_GGFS_NK";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMinuteKLine1: {
            eventString = @"APP_HQ_GGFS_1FZ";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMinuteKLine5: {
            eventString = @"APP_HQ_GGFS_5FZ";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMinuteKLine15: {
            eventString = @"APP_HQ_GGFS_15FZ";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMinuteKLine30: {
            eventString = @"APP_HQ_GGFS_30FZ";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMinuteKLine60: {
            eventString = @"APP_HQ_GGFS_60FZ";
            hasParam = YES;
            break;
        }
        case UPMarket2SDIChartTabItemTagMinuteKLine120: {
            eventString = @"APP_HQ_GGFS_120FZ";
            hasParam = YES;
            break;
        }
        // extraTabTag
        case UPMarket2SDIExtraTabItemTagSubjectChange: {
            eventString = @"APP_HQ_GGFS_DT";
            hasParam = NO;
            break;
        }
        case UPMarket2SDIExtraTabItemTagQueue: {
            eventString = @"APP_HQ_GGFS_DL";
            hasParam = NO;
            break;
        }
        case UPMarket2SDIExtraTabItemTagFund: {
            eventString = @"APP_HQ_GGFS_ZJ";
            hasParam = NO;
            break;
        }
        case UPMarket2SDIExtraTabItemTagNews: {
            eventString = @"APP_HQ_GGFS_XW";
            hasParam = NO;
            break;
        }
        case UPMarket2SDIExtraTabItemTagNotice: {
            eventString = @"APP_HQ_GGFS_GG";
            hasParam = NO;
            break;
        }
        case UPMarket2SDIExtraTabItemTagF10: {
            eventString = @"APP_HQ_GGFS_F10";
            hasParam = NO;
            break;
        }
    }
    
    if (eventString) {
        if (hasParam) {
            [UPThirdStatistics trackEvent:eventString label:nil parameters:@{
                @"HQ_ScreenPosition" : self.isLandscape ? @"横屏" : @"竖屏",
            }];
        } else {
            [UPThirdStatistics trackEvent:eventString];
        }
    }
}

// MARK: Getter & Setter

- (NSDictionary *)extraTabTitleDic {
    if (!_extraTabTitleDic) {
        _extraTabTitleDic = @{
//            @(UPMarket2SDIExtraTabItemTagNews)              : @"新闻",
            @(UPMarket2SDIExtraTabItemTagInfomation)        : @"资讯",
            @(UPMarket2SDIExtraTabItemTagNotice)            : @"公告",
            @(UPMarket2SDIExtraTabItemTagResearch)          : @"研报",
            @(UPMarket2SDIExtraTabItemTagSmartAssistant)    : @"监控",
            @(UPMarket2SDIExtraTabItemTagF10)               : @"简况",
            @(UPMarket2SDIExtraTabItemTagIntro)             : @"概述",
            @(UPMarket2SDIExtraTabItemTagFinancial)         : @"财务",
            @(UPMarket2SDIExtraTabItemTagComponent)         : @"成分股",
            @(UPMarket2SDIExtraTabItemTagQueue)             : @"队列",
            @(UPMarket2SDIExtraTabItemTagFund)              : @"资金",
            @(UPMarket2SDIExtraTabItemTagQA)                : @"互动",
            @(UPMarket2SDIExtraTabItemTagSubjectChange)     : @"异动",
            @(UPMarket2SDIExtraTabItemTagSubjectBlockChange): @"板块异动",
            @(UPMarket2SDIExtraTabItemTagDXJL)              : @"短线精灵",
        };
    }
    
    return _extraTabTitleDic;
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
        _bgView.backgroundColor = [UIColor up_bgColor];
    }
    return _bgView;
}

- (void)setStockModel:(UPMarketUIBaseModel *)stockModel {
    [super setStockModel:stockModel];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UPMarket2UpdateStockViewsNotificationName object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(stockViewsUpdate:)
                                                 name:UPMarket2UpdateStockViewsNotificationName
                                               object:stockModel.stockName];
    [self setupViewsContent];
    self.headerView.stockModel = stockModel;
    [self initIndexStatisticView:stockModel];
    [self initBottomTradeView:stockModel];
    [self initSceneView:stockModel];
    [self initLandscapeBottomToolView:stockModel];
    
    if (self.isLandscape) {
        self.landscapeBasicDataView.stockModel = stockModel;
    } else {
        self.basicDataView.stockModel = stockModel;
    }
    
    self.currentChartView.stockModel = stockModel;
    
    if (self.viewLoaded) {
        [self.view setNeedsLayout];
    }
    
    if (![self isNetworkAvailable] && !self.isLandscape) {
        [self.scrollView up_endPullRefresh];
    }
}

- (UPPopupView *)chartViewPopupView {
    if (!_chartViewPopupView) {
        _chartViewPopupView = [[UPPopupView alloc] init];
    }
    
    return _chartViewPopupView;
}

- (UPPopupView *)dealVolPopupView {
    if (!_dealVolPopupView) {
        _dealVolPopupView = [[UPPopupView alloc] init];
    }
    
    return _dealVolPopupView;
}

- (UIView *)dealVolContent {
    if (!_dealVolContent) {
        _dealVolContent = [UIView new];
        _dealVolContent.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:0.3];
        
        UIView *bgView = [UIView new];
        bgView.layer.cornerRadius = 12;
        bgView.backgroundColor = [UIColor up_bgColor];
        
        UILabel *tips = [UILabel new];
        tips.font = [UIFont up_boldFontOfSize:17];
        tips.textAlignment = NSTextAlignmentCenter;
        tips.text = @"说明";
        tips.textColor = UIColor.up_textPrimaryColor;
        
        UILabel *titleLabel = [UILabel new];
        titleLabel.font = [UIFont up_boldFontOfSize:13];
        titleLabel.textColor = [UIColor up_textPrimaryColor];
        titleLabel.textAlignment = NSTextAlignmentLeft;
        titleLabel.text = @"1.总量";
        
        UILabel *contentLabel = [UILabel new];
        contentLabel.font = [UIFont up_fontOfSize:13];
        contentLabel.textAlignment = NSTextAlignmentLeft;
        contentLabel.textColor = [UIColor up_textSecondaryColor];
        contentLabel.text = @"单位：千元面额\n示例：5000，表示总成交量为5000千元面额；5000万，表示总成交量为5000千万元面额。";
        contentLabel.numberOfLines = 0;
        
        UILabel *titleLabel2 = [UILabel new];
        titleLabel2.font = [UIFont up_boldFontOfSize:13];
        titleLabel2.textAlignment = NSTextAlignmentLeft;
        titleLabel2.textColor = [UIColor up_textPrimaryColor];
        titleLabel2.text = @"2.内盘、外盘";
        
        UILabel *contentLabel2 = [UILabel new];
        contentLabel2.font = [UIFont up_fontOfSize:13];
        contentLabel2.textAlignment = NSTextAlignmentLeft;
        contentLabel2.textColor = [UIColor up_textSecondaryColor];
        contentLabel2.text = @"单位：千元面额\n示例：5000，表示内盘或外盘成交量为5000千元面额；5000万，表示内盘或外盘成交量为5000千万元面额。";
        contentLabel2.numberOfLines = 0;

        UIView *line = [UIView new];
        line.backgroundColor = [UIColor up_dividerColor];
        
        
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn addTarget:self action:@selector(dealVolPopupViewClick) forControlEvents:UIControlEventTouchUpInside];
        [btn setTitleColor:[UIColor up_brandColor] forState:UIControlStateNormal];
        [btn setTitle:@"知道了" forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont up_boldFontOfSize:17];
        
        [_dealVolContent addSubview:bgView];
        [bgView addSubview:tips];
        [bgView addSubview:titleLabel];
        [bgView addSubview:contentLabel];
        [bgView addSubview:titleLabel2];
        [bgView addSubview:contentLabel2];
        [bgView addSubview:line];
        [bgView addSubview:btn];
        
        [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_dealVolContent);
            make.size.mas_equalTo(CGSizeMake(UPWidth(280), [UPThemeManager isWidgetShouldScale:self.view] ? UPWidth(380) : UPWidth(312)));
            make.centerY.mas_equalTo(_dealVolContent).offset(UPWidth(-20));
        }];

        [tips mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(bgView);
            make.top.equalTo(bgView).offset(25);
        }];
        
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(bgView).offset(20);
            make.right.equalTo(bgView).offset(-20);
            make.top.equalTo(tips.mas_bottom).offset(20);
        }];
        
        [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(bgView).offset(20);
            make.right.equalTo(bgView).offset(-20);
            make.top.equalTo(titleLabel.mas_bottom).offset(5);
        }];
        
        [titleLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(bgView).offset(20);
            make.right.equalTo(bgView).offset(-20);
            make.top.equalTo(contentLabel.mas_bottom).offset(7);
        }];
        
        [contentLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(bgView).offset(20);
            make.right.equalTo(bgView).offset(-20);
            make.top.equalTo(titleLabel2.mas_bottom).offset(5);
        }];
        
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.left.right.equalTo(bgView);
            make.height.mas_equalTo(43);
        }];
        
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(bgView);
            make.height.mas_equalTo(1);
            make.bottom.equalTo(btn.mas_top);
        }];
    }
    return _dealVolContent;
}

- (UPMarket2SDIHeaderView *)headerView {
    if(!_headerView) {
        _headerView = [[UPMarket2SDIHeaderView alloc] initWithStockModel:self.stockModel];
        _headerView.delegate = self;
        _headerView.backgroundColor = UIColor.fm_market_nav_color;
    }
    
    return _headerView;
}

- (UIScrollView *)scrollView {
    if(!_scrollView) {
        _scrollView = [[UIScrollView alloc] init];
        _scrollView.backgroundColor = UIColor.clearColor;
        _scrollView.showsVerticalScrollIndicator = NO;
        _scrollView.up_enablePullRefresh = YES;
        _scrollView.up_refreshStyle = UPRefreshStyleSingleStock;
        [_scrollView up_setPullRefreshTarget:self action:@selector(onRefreshScrollView)];
        _scrollView.scrollEnabled = YES;
        _scrollView.delegate = self;
        
        if (@available(iOS 11.0, *)) {
            _scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        }
    }
    
    return _scrollView;
}

-(UIView *)topContainerView {
    if(!_topContainerView) {
        _topContainerView = [[UIView alloc] init];
        _topContainerView.backgroundColor = UIColor.up_bgColor;
    }
    
    return _topContainerView;
}

- (UIView *)bottomContainerView {
    if (!_bottomContainerView) {
        _bottomContainerView = [[UIView alloc] init];
        _bottomContainerView.backgroundColor = UIColor.up_bgColor;
    }
    
    return _bottomContainerView;
}

- (UPMarket2SDIBasicDataView *)basicDataView {
    if(!_basicDataView) {
        _basicDataView = [[UPMarket2SDIBasicDataView alloc] initWithStockModel:self.stockModel];
        _basicDataView.backgroundColor = UIColor.up_contentBgColor;
        _basicDataView.delegate = self;
    }
    
    return _basicDataView;
}

- (UPMarket2SDILandscapeBasicDataView *)landscapeBasicDataView {
    if(!_landscapeBasicDataView) {
        _landscapeBasicDataView = [[UPMarket2SDILandscapeBasicDataView alloc] initWithStockModel:self.stockModel];
        _landscapeBasicDataView.delegate = self;
    }
    
    return _landscapeBasicDataView;
}

-(UPTabView *)chartsTabView {
    if(!_chartsTabView) {
        _chartsTabView = [[UPTabView alloc] init];
        _chartsTabView.minItemWidth = 35;
        _chartsTabView.itemFont = [UIFont up_fontOfSize:16];
        _chartsTabView.itemFontSelected = [UIFont up_boldFontOfSize:16];
        _chartsTabView.delegate = self;
        _chartsTabView.shouldFitTextWidth = YES;
    }
    
    return _chartsTabView;
}

- (UIView *)landscapeBottomToolView {
    if (!_landscapeBottomToolView) {
        UIView *view = [UIView new];
        
        NSArray *titleArray,*colorArray;
        int startTag = 0;
        
//        if (self.stockModel.isStockOfZQNHG) {
//            startTag = 3;
//            titleArray = @[@"立即参与",@"撤单"];
//            colorArray = @[UIColor.up_brandColor,UIColor.up_textPrimaryColor];
//        } else {
//            titleArray = @[@"买入",@"卖出",@"撤单"];
//            colorArray = @[UIColor.up_riseColor,UIColor.up_brandColor,UIColor.upmarket2_cancel_order_color];
//        }
        
        UIView *lastView;
        for (int i = 0; i < titleArray.count; i++) {
            
            UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
            [button setTitle:titleArray[i] forState:UIControlStateNormal];
            [button setTitleColor:colorArray[i] forState:UIControlStateNormal];
            button.titleLabel.font = [UIFont up_fontOfSize:13];
            button.backgroundColor = UIColor.up_bg1Color;
            button.layer.cornerRadius = 2;
            button.tag = i + startTag;
            [button addTarget:self action:@selector(landscapeBottomToolViewClicked:) forControlEvents:UIControlEventTouchUpInside];
            [view addSubview:button];
            
            [button mas_makeConstraints:^(MASConstraintMaker *make) {
                make.left.equalTo(!lastView ? view : lastView.mas_right).offset(10);
                make.centerY.equalTo(view);
                make.height.offset(25);
                if (lastView) {
                    make.width.equalTo(lastView);
                }
                if (i == titleArray.count - 1) {
                    make.right.equalTo(view).offset(-10);
                }
            }];
            
            lastView = button;
        }
        
        [lastView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(view);
        }];
        view.hidden = YES;
        view.userInteractionEnabled = NO;
        _landscapeBottomToolView = view;
    }
    
    return _landscapeBottomToolView;
}

//- (UPMarket2SDISceneView *)sceneView {
//    if (!_sceneView) {
//        _sceneView = [UPMarket2SDISceneView new];
//        _sceneView.delegate = self;
//    }
//
//    return _sceneView;
//}

-(UPTabView *)extraTabView {
    if(!_extraTabView) {
        _extraTabView = [[UPTabView alloc] init];
        _extraTabView.minItemWidth = 35;
        _extraTabView.delegate = self;
        _extraTabView.itemFont = [UIFont up_fontOfSize:16];
        _extraTabView.itemFontSelected = [UIFont up_boldFontOfSize:16];
        _extraTabView.isShowBottomLine = YES;
        
        [_extraTabView addSubview:self.etRightCover];
        [self.etRightCover mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.top.bottom.equalTo(_extraTabView);
            make.width.offset(50);
        }];
        
    }
    
    return _extraTabView;
}

- (UIView *)etRightCover {
    if (!_etRightCover) {
        _etRightCover = [UIView new];
    }
    return _etRightCover;
}

- (CAGradientLayer *)etRightCoverLayer {
    if (!_etRightCoverLayer) {
        _etRightCoverLayer = [CAGradientLayer layer];
        _etRightCoverLayer.frame = CGRectMake(0, 0, 50, UPMarket2SDIExtraTabViewHeight);
        _etRightCoverLayer.startPoint = CGPointMake(0, 0);
        _etRightCoverLayer.endPoint = CGPointMake(1, 0);
        _etRightCoverLayer.colors = @[
            (__bridge id)UIColor.upmarket2_tabCoverColor1.CGColor,
            (__bridge id)UIColor.upmarket2_tabCoverColor2.CGColor,
        ];
    }
    return _etRightCoverLayer;
}

- (UPMarketSDIOptionalToast *)optionToast {
    if (!_optionToast) {
        _optionToast = [[UPMarketSDIOptionalToast alloc] init];
        _optionToast.delegate = self;
    }
    
    return _optionToast;
}

- (UPMarket2SDIStockMaskView *)stockMaskView {
    if (!_stockMaskView) {
        _stockMaskView = [[UPMarket2SDIStockMaskView alloc] init];
    }
    return _stockMaskView;
}

- (void)setIsLandscape:(BOOL)isLandscape {
    super.isLandscape = isLandscape;
    
    self.currentChartView.isLandscape = isLandscape;
}

- (NSArray *)shareContentViews {
    return @[self.headerView,self.topContainerView];
}

- (UIButton *)indexSettingButton {
    if (!_indexSettingButton) {
        _indexSettingButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _indexSettingButton.tag = UPMarket2StockViewEventTagIndexSetting;
        [_indexSettingButton setBackgroundImage:UPTImg(@"个股/个股-设置") forState:UIControlStateNormal];
        _indexSettingButton.up_extendEdgeInsets = UIEdgeInsetsMake(10, 10, 30, 10);
        [_indexSettingButton addTarget:self action:@selector(gotoStockSettingVc) forControlEvents:UIControlEventTouchUpInside];
    }
    return _indexSettingButton;
}


// MARK: Charts Tab  

-(NSArray *)buildChartsTabViewItems {
    UPTabViewItem * item1 = [[UPTabViewItem alloc] init];
    item1.title = @"分时";
    item1.tag = UPMarket2SDIChartTabItemTagDailyMinute;
    
    UPTabViewItem * item2 = [[UPTabViewItem alloc] init];
    item2.title = @"日K";
    item2.tag = UPMarket2SDIChartTabItemTagDailyKLine;
    
    UPTabViewItem * item3 = [[UPTabViewItem alloc] init];
    item3.title = @"周K";
    item3.tag = UPMarket2SDIChartTabItemTagWeeklyKLine;
    
    UPTabViewItem * item4 = [[UPTabViewItem alloc] init];
    item4.title = @"月K";
    item4.tag = UPMarket2SDIChartTabItemTagMonthlyKLine;
    
    
    UPTabViewItem * item5 = [[UPTabViewItem alloc] init];
    item5.title = @"五日";
    item5.tag = UPMarket2SDIChartTabItemTagFiveDayMinute;
    
    UPTabViewItem * item6 = [[UPTabViewItem alloc] init];
    item6.title = @"更多";
    {
        UPTabViewItem * subItem1 = [[UPTabViewItem alloc] init];
        subItem1.title = @"1分钟";
        subItem1.tag = UPMarket2SDIChartTabItemTagMinuteKLine1;
        
        UPTabViewItem * subItem2 = [[UPTabViewItem alloc] init];
        subItem2.title = @"5分钟";
        subItem2.tag = UPMarket2SDIChartTabItemTagMinuteKLine5;
        
        UPTabViewItem * subItem3 = [[UPTabViewItem alloc] init];
        subItem3.title = @"15分钟";
        subItem3.tag = UPMarket2SDIChartTabItemTagMinuteKLine15;
        
        UPTabViewItem * subItem4 = [[UPTabViewItem alloc] init];
        subItem4.title = @"30分钟";
        subItem4.tag = UPMarket2SDIChartTabItemTagMinuteKLine30;
        
        UPTabViewItem * subItem5 = [[UPTabViewItem alloc] init];
        subItem5.title = @"60分钟";
        subItem5.tag = UPMarket2SDIChartTabItemTagMinuteKLine60;
        
        UPTabViewItem * subItem6 = [[UPTabViewItem alloc] init];
        subItem6.title = @"120分钟";
        subItem6.tag = UPMarket2SDIChartTabItemTagMinuteKLine120;
        
        UPTabViewItem * subItem7 = [[UPTabViewItem alloc] init];
        subItem7.title = @"季K";
        subItem7.tag = UPMarket2SDIChartTabItemTagSeasonKLine;
        
        UPTabViewItem * subItem8 = [[UPTabViewItem alloc] init];
        subItem8.title = @"年K";
        subItem8.tag = UPMarket2SDIChartTabItemTagYearKLine;
        
        item6.subItems = @[ subItem1, subItem2, subItem3, subItem4, subItem5, subItem6, subItem7, subItem8];
    }
    
    return @[ item1, item2, item3, item4, item5, item6 ];
}

-(void)showChartsTabWithTag:(UPMarket2SDIChartTabItemTag)tag {
    UPMarket2SDIBaseChartView * chartView = [self chartViewForTag:tag];
    chartView.customConfig = self.customConfig;

    if(chartView && chartView != self.currentChartView) {
        [self trackEvent:tag];

        if(self.currentChartView) {
            [self.currentChartView viewDidDisappear];
            [self.currentChartView removeFromSuperview];
        }

        [self.topContainerView addSubview:chartView];
        chartView.stockModel = self.stockModel;
        chartView.stockHq = self.stockHq;
        if (self.currentChartView) {
            chartView.displayScale = self.currentChartView.displayScale;
        }
        self.currentChartView = chartView;
        [chartView viewWillAppear];
        [self.currentChartView viewDidAppear];
        if (self.viewLoaded) {
            [self.view setNeedsLayout];
        }
    }
}

-(UPMarket2SDIBaseChartView *)chartViewForTag:(UPMarket2SDIChartTabItemTag)tag {
    NSString * key = [NSString stringWithFormat:@"CHART_VIEW_%@", @(tag)];
    UPMarket2SDIBaseChartView * chartView = (UPMarket2SDIBaseChartView *)[self cachedViewForKey:key];
    
    if(!chartView) {
        switch (tag) {
            case UPMarket2SDIChartTabItemTagDailyMinute: {
                UPMarket2SDIMinuteChartView * view = [UPMarket2SDIMinuteChartView chartView:1 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagFiveDayMinute: {
                UPMarket2SDIMinuteChartView * view = [UPMarket2SDIMinuteChartView chartView:5 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagDailyKLine: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeDaily stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagWeeklyKLine: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeWeek stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMonthlyKLine: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMonth stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagSeasonKLine: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeSeason stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagYearKLine: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeYear stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMinuteKLine1: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMinute1 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMinuteKLine5: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMinute5 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMinuteKLine15: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMinute15 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMinuteKLine30: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMinute30 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMinuteKLine60: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMinute60 stockModel:self.stockModel];
                chartView = view;
                break;
            }
            case UPMarket2SDIChartTabItemTagMinuteKLine120: {
                UPMarket2SDIKLineChartView * view = [UPMarket2SDIKLineChartView chartView:UPMarketUIKlineDataTypeMinute120 stockModel:self.stockModel];
                chartView = view;
                break;
            }
        }
        
        chartView.isLandscape = self.isLandscape;
        WeakSelf(weakSelf);
        chartView.clickBlock = ^(UPMarket2ChartViewEventTag tag, id object) {
            if (tag == UPMarket2StockViewEventTagIndexSetting) {        //点击指标设置
                [weakSelf gotoStockSettingVc];
            } else if (tag == UPMarket2StockViewEventTagMaskStock) {    //点击股票叠加
                [weakSelf showMaskStockView];
            } else if (tag == UPMarket2StockViewEventTagIndexName) {  //点击指标名字
                UPMarket2SDISelectIndexModel *model = object;
                
                [weakSelf showSelectIndexView:model];
            } else if (tag == UPMarket2StockViewEventTagIndexRegion) {
                [weakSelf updateStockList:YES];
                [weakSelf.currentChartView.stockViews makeObjectsPerformSelector:@selector(showRegion)];
            } else {
                [weakSelf showMaskStockView];
            }
        };
        
        chartView.longPressBlock = ^(id  _Nonnull data) {
            [weakSelf updateLongPressModel:data];
        };
        
        chartView.landscapeBlock = ^{
            if (weakSelf.isLandscape) {
                [weakSelf exitContentController];
            } else {
                [weakSelf enterLandscapeContentController];
            }
        };
        
        chartView.xLabelClickBlock = ^(BOOL isKline, UPMarketUIKlineModel *data){
            if (!isKline) {
                return;
            }
            
            if (!weakSelf.currentChartView.isHistoryMinute) {
                [weakSelf scrollTabViewToTop];
            }
            
            if (!weakSelf.historyMinuteView) {
                [weakSelf configHistoryMinuteView];
            }
            
            [weakSelf updateHistoryMinuteView:data];
        };
        
        chartView.crossStateChangeBlock = ^(UPMarketCrossLineState crossLineState) {
            
            if (weakSelf.isLandscape) {
                [weakSelf.landscapeBasicDataView setCrossState:crossLineState != UPMarketCrossLineStateEnd stockHq:weakSelf.stockHq];
            } else {
                [weakSelf.basicDataView setCrossState:crossLineState != UPMarketCrossLineStateEnd stockHq:weakSelf.stockHq];
            }
            
        };
        
        chartView.regionChangeBlock = ^(NSArray<UPMarketUIKlineModel *> *data) {
            [weakSelf regionDataUpdated:data];
        };
        
        chartView.clickQXModelBlock = ^(UPMarketUIKlineModel *klineModel, UPHqQXData *qxData) {
            [weakSelf clickQXModel:klineModel qxData:qxData];
        };
        
        chartView.completionBlock = ^{
            if (weakSelf.historyMinuteView.superview) {
                [weakSelf exitHistoryMinute];
            }
        };
        chartView.indexDeallocBlock = ^(NSInteger indexId) {
            [weakSelf.klineDataFetchLogic stopMonitorForIndex:indexId];
            [weakSelf.rtMinDataFetchLogic stopMonitorForIndex:indexId];
        };
        if ([chartView isKindOfClass:[UPMarket2SDIKLineChartView class]]) {
            UPMarket2SDIKLineChartView *klineChartView = (UPMarket2SDIKLineChartView *)chartView;
            klineChartView.klineUpdateIndexIdBlock = ^(UPMarketKlineType klineType, UPMarketIndexId lastIndexId, UPMarketIndexId indexId, NSInteger klineDataCount, BOOL isOverlay) {
                
                if (!weakSelf.currentChartView.isKline) {
                    return;
                }
                
                weakSelf.klineDataFetchLogic.klinePeriodType = klineType;
                if (lastIndexId) {
                    [weakSelf.klineDataFetchLogic stopMonitorForIndex:lastIndexId];
                }
                if (indexId) {
                    NSLog(@"指标请求--- %zd %zd", indexId, klineDataCount);
                    [weakSelf.klineDataFetchLogic startMonitorWithStock:weakSelf.stockModel forIndexId:indexId klineDataCount:klineDataCount isOverlay:isOverlay];
                }
                
                [weakSelf.rtMinDataFetchLogic clear];
            };
//            klineChartView.klineDataUpdatedBlock = ^(UPMarket2SDIBaseChartView * _Nonnull innerChartView, UPMarketUIBaseModel * _Nonnull stockModel, NSInteger startDate, NSInteger endDate) {
//                [weakSelf.requestDataLogic requsetDragonTigerRankListForStock:stockModel startDate:startDate endDate:endDate finishBlock:^(NSArray<NSNumber *> * _Nonnull customDataList) {
//                    innerChartView.customDataArray = customDataList;
//                }];
//                
//                [weakSelf.requestDataLogic requsetOptionalInfoForStock:stockModel finishBlock:^(long long optionalCreateTime) {
//                    innerChartView.addedOptionalTime = optionalCreateTime;
//                }];
//            };
        }else{
            UPMarket2SDIMinuteChartView *minChartView = (UPMarket2SDIMinuteChartView *)chartView;
            minChartView.rtMinUpdateIndexIdBlock = ^(UPMarketIndexId lastIndexId, UPMarketIndexId indexId) {
                
                if (weakSelf.currentChartView.isKline) {
                    return;
                }
                if (lastIndexId) {
                    [weakSelf.rtMinDataFetchLogic stopMonitorForIndex:lastIndexId];
                }
                if (indexId) {
                    [weakSelf.rtMinDataFetchLogic startMonitorWithStock:weakSelf.stockModel forIndexId:indexId];
                }
                
                [weakSelf.klineDataFetchLogic clear];
            };
        }
        
        chartView.loadMoreBlock = ^(NSInteger startDate, UPMarketIndexId indexId) {
            if (startDate != 0) {
                [weakSelf.klineDataFetchLogic loadMoreWithStock:weakSelf.stockModel withStartDate:startDate forIndexId:indexId isOverlay:NO];
            }
        };
        chartView.didSelectedIndexModel = ^(id  _Nonnull model) {
//            if (model && weakSelf.showLongPress) {
//                if (!weakSelf.dataTipsView.superview) {
//                    [weakSelf.topContainerView addSubview:weakSelf.dataTipsView];
//                    [weakSelf.dataTipsView mas_makeConstraints:^(MASConstraintMaker *make) {
//                        make.left.right.equalTo(weakSelf.view);
//                        if (weakSelf.isLandscape) {
//                            make.top.equalTo(weakSelf.landscapeBasicDataView.mas_bottom);
//                        }else{
//                            make.top.equalTo(weakSelf.chartsTabView);
//                        }
//                    }];
//                }
//                [weakSelf.dataTipsView refreshWithIndexInfo:model];
//            }else{
//                [weakSelf.dataTipsView removeFromSuperview];
//            }
        };
        chartView.getMinorCountBlock = ^NSInteger(BOOL isKline) {
            return isKline ? UPMarket2StockSettingStateManager.numberOfKLineMinorChart : UPMarket2StockSettingStateManager.numberOfMinuteMinorChart;
        };
        
        [self cacheView:chartView forKey:key];
    }
    
    return chartView;
}

- (void)setTemplateName:(NSString *)templateName {
    [super setTemplateName:templateName];

    // 如果当前有图表显示，立即设置模板
    
    if (self.currentChartView && templateName.length > 0) {
        self.currentChartView.templateName = templateName;
    }
}

// MARK: Extra Tab

-(NSArray *)buildExtraTabViewItems {
    
    UPMarketUIBaseModel *stockModel = self.stockModel;
    
    NSArray<NSNumber *> *dataArray = [NSArray array];
    if (stockModel.stockCategory == FMMarketStockCategory_BLOCK) {        
        return @[[UPTabViewItem itemWithTitle:@"成分股" tag:FMMarket2SDIExtraTabItemTagComponet]];
    }
    else if (stockModel.stockCategory == UPMarketStockCategory_FUND) {
        return @[[UPTabViewItem itemWithTitle:@"持仓股" tag:FMMarket2SDIExtraTabItemTagHoldings]];
    }
    else if (stockModel.stockCategory == UPMarketStockCategory_NONE)
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagInfomation)];
    }
    else if (stockModel.stockCategory == UPMarketStockCategory_INDEX)
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagFund)];
    }
    else if ([stockModel isStockOfMarketUS])
    {
//        dataArray = @[@(UPMarket2SDIExtraTabItemTagNews),@(UPMarket2SDIExtraTabItemTagIntro)];
        dataArray = @[@(UPMarket2SDIExtraTabItemTagIntro)];
    }
    else if (stockModel.stockCategory == UPMarketStockCategory_XSB)
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagNotice)];
    }
    else if (stockModel.stockSetCode == UPMarketSetCodeSHJ || [stockModel isMainCoin])
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagInfomation),@(UPMarket2SDIExtraTabItemTagIntro)];
    }
    else if (stockModel.stockCategory == UPMarketStockCategory_Concept || stockModel.stockCategory == UPMarketStockCategory_Industry || stockModel.stockCategory == UPMarketStockCategory_Region)
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagComponent)];
    }
    else if ([stockModel isIndexOfMarketHS])
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagInfomation),@(UPMarket2SDIExtraTabItemTagFund)];
    }
    else if ([stockModel isStockOfMarketHSA] || [stockModel isStockOfMarketHSB])
    {
        NSMutableArray *mutableArray = [NSMutableArray array];
        
        [mutableArray addObject:@(UPMarket2SDIExtraTabItemTagSmartAssistant)];
//        [mutableArray addObject:@(UPStockInfoViewTypeQueue)]; 去掉队列
        [mutableArray addObjectsFromArray:@[@(UPMarket2SDIExtraTabItemTagF10),@(UPMarket2SDIExtraTabItemTagFund)]];
//        [mutableArray addObjectsFromArray:@[@(UPMarket2SDIExtraTabItemTagNews),@(UPMarket2SDIExtraTabItemTagNotice),@(UPMarket2SDIExtraTabItemTagResearch)]];
        [mutableArray addObjectsFromArray:@[@(UPMarket2SDIExtraTabItemTagNotice),@(UPMarket2SDIExtraTabItemTagResearch)]];

        if ([stockModel isStockOfMarketHSA]) {
            [mutableArray addObject:@(UPMarket2SDIExtraTabItemTagQA)]; // 去掉互动
        }
        dataArray = [mutableArray copy];
    }
    else
    {
        dataArray = @[@(UPMarket2SDIExtraTabItemTagInfomation)];
    }
    
    NSMutableArray<UPTabViewItem *> *items = [NSMutableArray array];
    for (NSNumber *number in dataArray) {
        [items addObject:[UPTabViewItem itemWithTitle:self.extraTabTitleDic[number] tag:number.integerValue]];
    }
    
    // 警告⚠️: posAtGlobalExtraTab 新增时一定要修改 sGlobalExtraTab 数组的数量, 不然会导致数组越界
    UPMarket2SDIContentController.sGlobalExtraTab[self.posAtGlobalExtraTab] = @(MIN([UPMarket2SDIContentController.sGlobalExtraTab[self.posAtGlobalExtraTab] integerValue], dataArray.count));
    
    return items;
}

-(void)showExtraTabWithTag:(UPMarket2SDIExtraTabItemTag)tag {
    UIView * extraView = [self extraViewForTag:tag];
    
    if (extraView) {
        if (extraView == self.currentExtraView) {
            [self extraViewWillAppear:extraView];
            [self extraViewDidAppear:extraView];
            
            return;
        }
        
        [self trackEvent:tag];
        
        if(self.currentExtraView) {
            // 旧逻辑兼容
            [self extraViewDidDisappear:self.currentExtraView];
            
            [self.currentExtraView removeFromSuperview];
        }
        
        [self.bottomContainerView addSubview:extraView];
        
        extraView.tag = tag; // 重要, 内部通过tag来判断显示的内容
        
        // 旧逻辑兼容
        [self extraViewWillAppear:extraView];
        [self extraViewDidAppear:extraView];
        
        self.currentExtraView = extraView;
        
        if (self.viewLoaded) {
            [self.view setNeedsLayout];
        }
    }
}

-(UIView *)extraViewForTag:(UPMarket2SDIExtraTabItemTag)tag {
    NSString * key = [NSString stringWithFormat:@"EXTRA_VIEW_%@", @(tag)];
    UIView * extraView = [self cachedViewForKey:key];
    
    if(!extraView) {
        Class clazz = nil;
        
        switch (tag) {
            case FMMarket2SDIExtraTabItemTagComponet:
                clazz = NSClassFromString(@"FMBlockDetailConstituentStockView");
                break;
            case FMMarket2SDIExtraTabItemTagHoldings:
                clazz = NSClassFromString(@"FMFundDetailHoldingStockView");
                break;
            case UPMarket2SDIExtraTabItemTagNews:
                clazz = NSClassFromString(@"UPStockNewsView");
                break;
            case UPMarket2SDIExtraTabItemTagInfomation:
                clazz = NSClassFromString(@"UPStockNewsView");
                break;
            case UPMarket2SDIExtraTabItemTagNotice:
                clazz = NSClassFromString(@"FMStockDetailAnnouncementView");
                break;
            case UPMarket2SDIExtraTabItemTagResearch:
                clazz = NSClassFromString(@"FMStockDetailReportsView");
                break;
            case UPMarket2SDIExtraTabItemTagSmartAssistant:
                clazz = NSClassFromString(@"UPMarket2SDIH5InfoWebView");
                break;
            case UPMarket2SDIExtraTabItemTagF10:
                clazz = NSClassFromString(@"FMStockDetailF10View");
                break;
            case UPMarket2SDIExtraTabItemTagIntro:
                if(self.stockModel.stockSetCode == UPMarketSetCodeHK ||
                   self.stockModel.stockSetCode == UPMarketSetCodeUS ||
                   self.stockModel.stockSetCode == UPMarketSetCodeNY ||
                   self.stockModel.stockSetCode == UPMarketSetCodeNSDK) {
                    clazz = NSClassFromString(@"UPMarket2SDIH5InfoWebView");
                } else {
                    clazz = NSClassFromString(@"UPStockIntroView");
                }
                break;
            case UPMarket2SDIExtraTabItemTagFinancial:
                clazz = NSClassFromString(@"UPMarket2SDIH5InfoWebView");
                break;
            case UPMarket2SDIExtraTabItemTagComponent:
                clazz = NSClassFromString(@"UPMarket2SDIComponentView");
                break;
            case UPMarket2SDIExtraTabItemTagQueue:
                clazz = NSClassFromString(@"UPMarket2SDIQueueView");
                break;
            case UPMarket2SDIExtraTabItemTagFund:
                clazz = NSClassFromString(@"FMStockDetailFundView");
                break;
            case UPMarket2SDIExtraTabItemTagQA:
                clazz = NSClassFromString(@"FMStockDetailInteractionView");
                break;
            case UPMarket2SDIExtraTabItemTagSubjectChange:
                clazz = NSClassFromString(@"UPMarket2SDIH5InfoWebView");
                break;
            case UPMarket2SDIExtraTabItemTagSubjectBlockChange:
                clazz = NSClassFromString(@"UPMarket2SDISubjectBlockChangeView");
                break;
            case UPMarket2SDIExtraTabItemTagDXJL:
                clazz = NSClassFromString(@"UPMarket2SDIH5InfoWebView");
                break;
        } 
        
        if(clazz) {
            extraView = [[clazz alloc] init];

            [self cacheView:extraView forKey:key];
        }
    }
    
    return extraView;
}

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
-(void)extraViewDidAppear:(UIView *)extraView {
    if (extraView) {
        if ([extraView respondsToSelector:@selector(setStockModel:)]) {
            [extraView performSelector:@selector(setStockModel:) withObject:self.stockModel];
        }
        
        if ([extraView respondsToSelector:@selector(viewWillAppear)]) {
            [extraView performSelector:@selector(viewWillAppear)];
        }
        
        if ([extraView respondsToSelector:@selector(viewDidAppear)]) {
            [extraView performSelector:@selector(viewDidAppear)];
        }
        
        if ([extraView respondsToSelector:@selector(setStockHq:)]) {
            [extraView performSelector:@selector(setStockHq:) withObject:self.stockHq];
        }
        
        if ([extraView respondsToSelector:@selector(requestData)]) {
            [extraView performSelector:@selector(requestData)];
        }
        
        if ([extraView respondsToSelector:@selector(setOuterController:)]) {
            [extraView performSelector:@selector(setOuterController:) withObject:self.outerContainerViewController];
        }
        
        [self postScrollNotification:self.scrollView];
    }
}

-(void)extraViewWillAppear:(UIView *)extraView {
    if(extraView && [extraView respondsToSelector:@selector(viewWillAppear)]) {
        [extraView performSelector:@selector(viewWillAppear)];
    }
}

-(void)extraViewWillDisappear:(UIView *)extraView {
    if(extraView && [extraView respondsToSelector:@selector(viewWillDisappear)]) {
        [extraView performSelector:@selector(viewWillDisappear)];
    }
}


-(void)extraViewDidDisappear:(UIView *)extraView {
    if(extraView && [extraView respondsToSelector:@selector(viewDidDisappear)]) {
        [extraView performSelector:@selector(viewDidDisappear)];
    }
}
#pragma clang diagnostic pop

// MARK: View Cache

-(UIView *)cachedViewForKey:(NSString *)key {
    if(key) {
        if(!self.viewCache) {
            self.viewCache = [NSMutableDictionary dictionaryWithCapacity:4];
        }
        
        return [self.viewCache objectForKey:key];
    }
    
    return nil;
}

-(void)cacheView:(UIView *)view forKey:(NSString *)key {
    if(view && key) {
        if(!self.viewCache) {
            self.viewCache = [NSMutableDictionary dictionaryWithCapacity:4];
        }
        
        [self.viewCache setObject:view forKey:key];
    }
}

- (void)landscapeBottomToolViewClicked:(UIButton *)btn {
    
    if (btn.tag == 0) {
        [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_MR"];
        if (self.stockModel.isStockOfGGT) {
            NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            [UPRouterUtil goGgtBuyWithMarket:marketId code:self.stockModel.stockCode];
        } else if ([UPMarketCategoryUtil isBJMarket:self.stockModel.stockSetCode]) {
            // 普通买入
            NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            if (self.stockModel.stockCategory == UPMarketStockCategory_BOND && self.stockModel.stockSubCategory == UPMarketStockSubCategory_ZQ_KZZ) {
                [UPRouterUtil goBjsKZZBuyWithMarket:marketId code:self.stockModel.stockCode name:self.stockModel.stockName];
            } else {
                [UPRouterUtil goTradeBuy:self.stockModel.stockSetCode code:self.stockModel.stockCode price:0 fast:NO type:UPRouterTradeTypeNormal];
            }
        } else if (self.stockModel.stockSetCode == UPMarketSetCodeXSB) {
            // 普通买入
            NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            if (self.stockModel.stockCategory == UPMarketStockCategory_BOND && self.stockModel.stockSubCategory == UPMarketStockSubCategory_XSB_KZZ) {
                [UPRouterUtil goXsbKZZBuyWithMarket:marketId code:self.stockModel.stockCode name:self.stockModel.stockName];
            } else {
                [UPRouterUtil goXsbNormalBuyWithMarket:marketId code:self.stockModel.stockCode];
            }
        } else if(self.stockModel.stockSetCode == UPMarketSetCodeBH){
            // B转H股跳转B股买卖
            UPMarketRelationStockInfo *bhInfo;
            for (UPMarketRelationStockInfo *info in self.stockModel.stockHq.relationStockArray) {
                if (info.relationType == UPMarketRelationStockTypeBH) {
                    bhInfo = info;
                    break;
                }
            }
            
            if (bhInfo) {
                [UPRouterUtil goTradeBuy:bhInfo.setCode code:bhInfo.code price:0 fast:NO type:UPRouterTradeTypeNormal];
            }
        } else {
            [UPRouterUtil goTradeBuy:self.stockModel.stockSetCode code:self.stockModel.stockCode price:0 fast:NO type:UPRouterTradeTypeNormal];
        }
        
    } else if (btn.tag == 1) {
        [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_MC"];
        
        if (self.stockModel.isStockOfGGT) {
            NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            [UPRouterUtil goGgtSellWithMarket:marketId code:self.stockModel.stockCode];
        } else if ([UPMarketCategoryUtil isBJMarket:self.stockModel.stockSetCode]) {
            // 普通卖出
            NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            if (self.stockModel.stockCategory == UPMarketStockCategory_BOND && self.stockModel.stockSubCategory == UPMarketStockSubCategory_ZQ_KZZ) {
                [UPRouterUtil goBjsKZZSellWithMarket:marketId code:self.stockModel.stockCode name:self.stockModel.stockName];
            } else {
                [UPRouterUtil goTradeSell:self.stockModel.stockSetCode code:self.stockModel.stockCode price:0 fast:NO type:UPRouterTradeTypeNormal];
            }
        } else if (self.stockModel.stockSetCode == UPMarketSetCodeXSB) {
            // 普通卖出
           NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            if (self.stockModel.stockCategory == UPMarketStockCategory_BOND && self.stockModel.stockSubCategory == UPMarketStockSubCategory_XSB_KZZ) {
                [UPRouterUtil goXsbKZZSellWithMarket:marketId code:self.stockModel.stockCode name:self.stockModel.stockName];
            } else {
                [UPRouterUtil goXsbNormalSellWithMarket:marketId code:self.stockModel.stockCode];
            }
        } else if(self.stockModel.stockSetCode == UPMarketSetCodeBH){
            // B转H股跳转B股买卖
            UPMarketRelationStockInfo *bhInfo;
            for (UPMarketRelationStockInfo *info in self.stockModel.stockHq.relationStockArray) {
                if (info.relationType == UPMarketRelationStockTypeBH) {
                    bhInfo = info;
                    break;
                }
            }
            
            if (bhInfo) {
                [UPRouterUtil goTradeSell:bhInfo.setCode code:bhInfo.code price:0 fast:NO type:UPRouterTradeTypeNormal];
            }
        } else {
            [UPRouterUtil goTradeSell:self.stockModel.stockSetCode code:self.stockModel.stockCode price:0 fast:NO type:UPRouterTradeTypeNormal];
        }
        
    } else if (btn.tag == 2) {
        [UPThirdStatistics trackEvent:@"APP_HQ_GGFS_HP_CD"];
        if (self.stockModel.isStockOfGGT) {
            NSString *marketId = [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:self.stockModel.stockSetCode category:self.stockModel.stockCategory stockCode:self.stockModel.stockCode from:YES];
            [UPRouterUtil goGgtCancelWithMarket:marketId code:self.stockModel.stockCode];
        } else if ([UPMarketCategoryUtil isBJMarket:self.stockModel.stockSetCode]) {
            [UPRouterUtil goBjsCancel];
        } else if (self.stockModel.isStockOfMarketXSB) {
            [UPRouterUtil goXsbCancel];
        } else {
            [UPRouterUtil goTradeCancel:NO credit:NO];
        }
        
    } else if (btn.tag == 3) {
        NSAssert(NO, @"需要资金账号登录");
//        [self goGZNHGTradeVC:self.stockHq stockModel:self.stockModel];
    } else if (btn.tag == 4) {
        NSAssert(NO, @"需要资金账号登录");
//        [UPRouterUtil goAfterTradeLogin:UPRouterTradeTypeNormal urlStr:@"upchina://trade/cancel?type=normal"];
    }
}

- (void)goGZNHGTradeVC:(UPHqStockHq *)stockHq stockModel:(UPMarketUIBaseModel *)model {
    if (!stockHq || !model.isStockOfZQNHG) {
        return;
    }
    
    NSString *baseURL = @"upchina://trade/web";
    
    NSString *gznhgURL = [@"gznhg/wtxd" up_buildURLWithQueryParams:@{
        @"code" : stockHq.code,
        @"market" : [UPCommonConvertUtil up_convertMarketToTradeWithMarketSetCode:stockHq.setCode category:stockHq.category stockCode:stockHq.code from:NO],
        @"name" : stockHq.name,
        @"days" : stockHq.zqExData ? @(stockHq.zqExData.days) : @(0)
    }];
    
    NSString *url = [baseURL up_buildURLWithQueryParams:@{
        @"url" : gznhgURL
    }];
    
//    [UPRouterUtil goAfterTradeLogin:UPRouterTradeTypeNormal urlStr:url];
}

- (void)gotoStockSettingVc {
    UPMarket2StockSettingController *vc = [[UPMarket2StockSettingController alloc] init];
    vc.stockHq = self.currentChartView.stockHq;
    
    if ([self.currentChartView isKindOfClass:UPMarket2SDIKLineChartView.class]) {
        vc.tabIndex = UPMarket2StockSettingTabIndexKline;
    } else if ([self.currentChartView isKindOfClass:UPMarket2SDIMinuteChartView.class]) {
        vc.tabIndex = UPMarket2StockSettingTabIndexMinute;
    }
    
    [self.navigationController pushViewController:vc animated:YES];
}

// 类别：0-默认、1-沪深AB股、2-板块, 3-港股，4-港股通, 5-债券, 6-基金, 7-股转, 8-上证指数, 9-深证成指 or 创业扳指  10-其他沪深指数 11-期权 12-港股指数/全球指数 16-沪深B股
+ (NSMutableArray *)sGlobalExtraTab {
    if (!_sGlobalExtraTab) {
        _sGlobalExtraTab = @[@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0,@0].mutableCopy;
        /// 大决策不需要这个逻辑
//        _sGlobalExtraTab[0] = @1;
    }
    
    return _sGlobalExtraTab;
}

- (void)setIndex:(NSUInteger)index {
    [super setIndex:index];
    
    [self.headerView configStockCount:self.models.count currentIndex:index];
    
    [self.landscapeBasicDataView configStockCount:self.models.count currentIndex:index];
}


- (UPMarketIndexKlineIndexFetchLogic *)klineDataFetchLogic {
    if (!_klineDataFetchLogic) {
        __weak __typeof(self)weakSelf = self;
        _klineDataFetchLogic = [[UPMarketIndexKlineIndexFetchLogic alloc] initWithRefreshBlock:^(UPMarketIndexDataModel * _Nonnull indexDataModel, BOOL isOverlay) {
            NSLog(@"  数据条数---%zd", indexDataModel.marketIndexInfoMap.allKeys.count);
            [weakSelf.currentChartView updateIndexDataModel:indexDataModel isOverlay:isOverlay];
        }];
    }
    return _klineDataFetchLogic;
}


- (UPMarketIndexMinIndexFetchLogic *)rtMinDataFetchLogic {
    if (!_rtMinDataFetchLogic) {
        __weak __typeof(self)weakSelf = self;
        _rtMinDataFetchLogic = [[UPMarketIndexMinIndexFetchLogic alloc] initWithRefreshBlock:^(UPMarketIndexDataModel * _Nonnull indexDataModel, BOOL isOverlay) {
            NSLog(@"分时  数据条数---%zd", indexDataModel.marketIndexInfoMap.allKeys.count);
            [weakSelf.currentChartView updateIndexDataModel:indexDataModel isOverlay:isOverlay];
        }];
    }
    return _rtMinDataFetchLogic;
}

@end
